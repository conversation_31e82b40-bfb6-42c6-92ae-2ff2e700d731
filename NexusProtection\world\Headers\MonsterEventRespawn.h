/*
 * MonsterEventRespawn.h - Monster Event Respawn System
 * Refactored for Visual Studio 2022 compatibility
 * Original: SetEventRespawnCMonsterEventRespawnQEAA_NXZ_1402A5DE0.c
 */

#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <cstdint>

// Forward declarations
class CMapData;
class CRecordData;
class CLogFile;
class CMapOperation;

// Base field structure (simplified representation)
struct BaseField {
    uint32_t dwIndex;
    char szCode[32];
    // Additional fields as needed
};

// Event respawn monster data
struct EventRespawnMonster {
    BaseField* pMonsterData;
    uint16_t wCount;
    uint16_t wPadding;
};

// Event respawn reward item
struct EventRespawnRewardItem {
    uint8_t byItemTableIndex;
    uint8_t byPadding[7];
    BaseField* pItemData;
    BaseField* pMonsterData;  // Specific monster or nullptr for all
    uint32_t dwDropRate;      // Drop rate percentage
    uint32_t dwPadding;
};

// Event respawn configuration
struct EventRespawnConfig {
    bool bLoad;                                    // Is loaded
    uint8_t byPadding[3];
    uint16_t wMonsterSetCount;                     // Number of monster sets
    uint16_t wPadding;
    EventRespawnMonster monsters[640];             // Monster configurations
    CMapData* pMapData;                           // Target map
    float fPosition[3];                           // X, Y, Z position
    uint32_t dwRespawnInterval;                   // Respawn interval in milliseconds
    bool bKillAfterStop;                          // Kill monsters when event stops
    bool bExpPenalty;                             // Apply experience penalty
    bool bExpReward;                              // Give experience reward
    bool bItemLoot;                               // Allow item looting
    uint32_t dwRewardItemCount;                   // Number of reward items
    EventRespawnRewardItem rewardItems[128];      // Reward item configurations
    char szScriptName[64];                        // Script filename
};

/**
 * Monster Event Respawn System
 * Manages scripted monster respawn events with configurable parameters
 */
class CMonsterEventRespawn {
public:
    // Constructor/Destructor
    CMonsterEventRespawn();
    virtual ~CMonsterEventRespawn();

    // Main functionality
    bool SetEventRespawn();
    bool StartRespawnEvent(const char* pszEventCode, char* pwszErrCode);
    bool StopRespawnEvent(const char* pszEventCode, char* pwszErrCode);

    // Accessors
    int GetLoadedEventCount() const { return m_nLoadEventRespawn; }
    const EventRespawnConfig* GetEventConfig(int index) const;

private:
    // Constants
    static constexpr int MAX_EVENT_RESPAWN = 32;
    static constexpr int MAX_MONSTERS_PER_EVENT = 640;
    static constexpr int MAX_REWARD_ITEMS = 128;
    static constexpr int MAX_FILENAME_LENGTH = 64;

    // Member variables
    EventRespawnConfig m_EventRespawn[MAX_EVENT_RESPAWN];
    int m_nLoadEventRespawn;

    // Helper methods
    bool LoadEventRespawnFiles();
    bool ParseEventRespawnFile(const char* filename, EventRespawnConfig& config);
    bool ValidateMonsterConfiguration(const EventRespawnConfig& config);
    bool ValidatePositionConfiguration(const EventRespawnConfig& config);
    bool LoadRewardItems(const char* filename, EventRespawnConfig& config);
    
    // Utility methods
    static bool IsValidMapCode(const char* mapCode);
    static uint8_t GetItemTableCode(const char* itemCode);
    static void LogError(const char* format, ...);
    static void LogInfo(const char* format, ...);

    // Disable copy constructor and assignment operator
    CMonsterEventRespawn(const CMonsterEventRespawn&) = delete;
    CMonsterEventRespawn& operator=(const CMonsterEventRespawn&) = delete;
};

// Global instances (if needed)
extern CMonsterEventRespawn* g_pMonsterEventRespawn;

// Utility functions
namespace MonsterEventRespawnUtils {
    bool ValidateEventRespawnDirectory();
    std::vector<std::string> GetEventRespawnFiles();
    bool BackupEventRespawnConfig(const char* filename);
}
