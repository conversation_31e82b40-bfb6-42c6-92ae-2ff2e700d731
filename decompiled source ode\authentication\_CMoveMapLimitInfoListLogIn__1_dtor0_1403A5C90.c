/*
 * Function: _CMoveMapLimitInfoList::LogIn_::_1_::dtor$0
 * Address: 0x1403A5C90
 */

void __fastcall CMoveMapLimitInfoList::LogIn_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>((std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)(a2 + 144));
}
