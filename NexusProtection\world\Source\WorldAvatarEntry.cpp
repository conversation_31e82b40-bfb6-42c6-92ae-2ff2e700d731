/*
 * WorldAvatarEntry.cpp - World Avatar Entry System Implementation
 * Refactored for Visual Studio 2022 compatibility
 * Original: wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_140046110.c
 */

#include "../Headers/WorldAvatarEntry.h"
#include <iostream>
#include <cstring>
#include <algorithm>
#include <cmath>
#include <cassert>

// External dependencies (these would need to be properly defined)
extern CPartyPlayer g_PartyPlayer[2532];  // Global party player array

// Forward declarations for external functions
namespace {
    // These would be properly implemented based on the actual CPartyPlayer class
    bool CPartyPlayer_EnterWorld(CPartyPlayer* pPlayer, const WorldAvatarCode* pData, uint16_t wZoneIndex);
    bool CPartyPlayer_ExitWorld(CPartyPlayer* pPlayer, CPartyPlayer** ppNewBoss);
    bool CPartyPlayer_IsPartyMode(const CPartyPlayer* pPlayer);
    CPartyPlayer** CPartyPlayer_GetPtrPartyMember(CPartyPlayer* pPlayer);
}

/**
 * WorldAvatarCode Implementation
 */
WorldAvatarCode::WorldAvatarCode() {
    Reset();
}

void WorldAvatarCode::Reset() {
    m_id.Reset();
    memset(m_wszName, 0, sizeof(m_wszName));
    m_byClass = 0;
    m_byLevel = 1;
    m_wMapCode = 0;
    m_fPosition[0] = m_fPosition[1] = m_fPosition[2] = 0.0f;
    m_dwGuildSerial = 0;
    m_byPvpMode = 0;
    m_byPartyMode = 0;
    memset(m_byReserved, 0, sizeof(m_byReserved));
}

bool WorldAvatarCode::IsValidAvatar() const {
    return m_id.IsValid() && 
           IsValidClass() && 
           IsValidLevel() && 
           IsValidMap() &&
           strlen(m_wszName) > 0;
}

void WorldAvatarCode::SetPosition(float x, float y, float z) {
    m_fPosition[0] = x;
    m_fPosition[1] = y;
    m_fPosition[2] = z;
}

void WorldAvatarCode::SetName(const char* name) {
    if (name && strlen(name) < sizeof(m_wszName)) {
        strncpy_s(m_wszName, sizeof(m_wszName), name, _TRUNCATE);
    }
}

bool WorldAvatarCode::IsValidClass() const {
    // Assuming classes 1-6 are valid (this would be game-specific)
    return m_byClass >= 1 && m_byClass <= 6;
}

bool WorldAvatarCode::IsValidLevel() const {
    // Assuming levels 1-200 are valid (this would be game-specific)
    return m_byLevel >= 1 && m_byLevel <= 200;
}

bool WorldAvatarCode::IsValidMap() const {
    // Basic validation - this would need proper map validation
    return m_wMapCode > 0 && m_wMapCode < 10000;
}

/**
 * WorldAvatarEntryManager Implementation
 */
WorldAvatarEntryManager::WorldAvatarEntryManager() {
    // Constructor implementation
}

WorldAvatarEntryManager::~WorldAvatarEntryManager() {
    // Destructor implementation
}

/**
 * Main entry function - refactored from original wa_EnterWorld
 */
bool WorldAvatarEntryManager::EnterWorld(const WorldAvatarCode* pAvatarData, uint16_t wZoneIndex) {
    // Input validation
    if (!ValidateAvatarEntry(pAvatarData, wZoneIndex)) {
        LogEntryAttempt(pAvatarData, wZoneIndex, EntryResult::InvalidAvatar);
        return false;
    }

    try {
        // Get the party player instance (equivalent to original array access)
        CPartyPlayer* pPartyPlayer = GetPartyPlayer(pAvatarData->m_id.wIndex);
        if (!pPartyPlayer) {
            LogEntryAttempt(pAvatarData, wZoneIndex, EntryResult::SystemError);
            return false;
        }

        // Check if player is already logged in (equivalent to original !v5->m_bLogin check)
        if (IsPlayerLoggedIn(pPartyPlayer, pAvatarData)) {
            LogEntryAttempt(pAvatarData, wZoneIndex, EntryResult::AlreadyLoggedIn);
            return false;
        }

        // Initialize avatar entry (equivalent to original CPartyPlayer::EnterWorld call)
        if (!InitializeAvatarEntry(pPartyPlayer, pAvatarData, wZoneIndex)) {
            LogEntryAttempt(pAvatarData, wZoneIndex, EntryResult::SystemError);
            return false;
        }

        LogEntryAttempt(pAvatarData, wZoneIndex, EntryResult::Success);
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in EnterWorld: " << e.what() << std::endl;
        LogEntryAttempt(pAvatarData, wZoneIndex, EntryResult::SystemError);
        return false;
    }
}

/**
 * Validation function for avatar entry
 */
bool WorldAvatarEntryManager::ValidateAvatarEntry(const WorldAvatarCode* pAvatarData, uint16_t wZoneIndex) {
    if (!pAvatarData) {
        return false;
    }

    if (!pAvatarData->IsValidAvatar()) {
        return false;
    }

    if (!IsValidZoneIndex(wZoneIndex)) {
        return false;
    }

    if (!CanEnterZone(pAvatarData, wZoneIndex)) {
        return false;
    }

    return true;
}

/**
 * Get party player by index (equivalent to original array access)
 */
CPartyPlayer* WorldAvatarEntryManager::GetPartyPlayer(uint16_t wIndex) {
    if (wIndex >= MAX_PLAYER_INDEX) {
        return nullptr;
    }

    // Equivalent to: (CPartyPlayer *)((char *)&g_PartyPlayer + 128 * (unsigned __int64)pDataa->m_id.wIndex)
    return &g_PartyPlayer[wIndex];
}

/**
 * Check if player is already logged in
 */
bool WorldAvatarEntryManager::IsPlayerLoggedIn(const CPartyPlayer* pPlayer, const WorldAvatarCode* pAvatarData) {
    if (!pPlayer) {
        return false;
    }

    // This would need to be implemented based on actual CPartyPlayer structure
    // Original check was: !v5->m_bLogin
    // For now, return false to allow entry (this would be properly implemented)
    return false;
}

/**
 * Initialize avatar entry (equivalent to original CPartyPlayer::EnterWorld call)
 */
bool WorldAvatarEntryManager::InitializeAvatarEntry(CPartyPlayer* pPlayer, const WorldAvatarCode* pAvatarData, uint16_t wZoneIndex) {
    if (!pPlayer || !pAvatarData) {
        return false;
    }

    // Call the actual CPartyPlayer::EnterWorld method
    return CPartyPlayer_EnterWorld(pPlayer, pAvatarData, wZoneIndex);
}

/**
 * Zone validation functions
 */
bool WorldAvatarEntryManager::IsValidZoneIndex(uint16_t wZoneIndex) {
    return wZoneIndex > 0 && wZoneIndex <= MAX_ZONE_INDEX;
}

bool WorldAvatarEntryManager::CanEnterZone(const WorldAvatarCode* pAvatarData, uint16_t wZoneIndex) {
    if (!pAvatarData) {
        return false;
    }

    // Basic validation - this would include level requirements, access rights, etc.
    // For now, just check if the zone index is valid
    return IsValidZoneIndex(wZoneIndex);
}

/**
 * Logging functions
 */
void WorldAvatarEntryManager::LogEntryAttempt(const WorldAvatarCode* pAvatarData, uint16_t wZoneIndex, EntryResult result) {
    const char* resultStr = GetEntryResultString(result);
    
    if (pAvatarData) {
        std::cout << "[WORLD_ENTRY] Avatar: " << pAvatarData->m_wszName 
                  << " (ID: " << pAvatarData->m_id.wIndex << ":" << pAvatarData->m_id.dwSerial << ")"
                  << " Zone: " << wZoneIndex 
                  << " Result: " << resultStr << std::endl;
    } else {
        std::cout << "[WORLD_ENTRY] Invalid avatar data, Zone: " << wZoneIndex 
                  << " Result: " << resultStr << std::endl;
    }
}

void WorldAvatarEntryManager::LogExitAttempt(const ClientID* pClientID, ExitResult result) {
    const char* resultStr = GetExitResultString(result);
    
    if (pClientID) {
        std::cout << "[WORLD_EXIT] Client: " << pClientID->wIndex << ":" << pClientID->dwSerial
                  << " Result: " << resultStr << std::endl;
    } else {
        std::cout << "[WORLD_EXIT] Invalid client ID, Result: " << resultStr << std::endl;
    }
}

/**
 * Result string functions
 */
const char* WorldAvatarEntryManager::GetEntryResultString(EntryResult result) {
    switch (result) {
        case EntryResult::Success: return "Success";
        case EntryResult::InvalidAvatar: return "Invalid Avatar";
        case EntryResult::InvalidZone: return "Invalid Zone";
        case EntryResult::AlreadyLoggedIn: return "Already Logged In";
        case EntryResult::PartyConflict: return "Party Conflict";
        case EntryResult::SystemError: return "System Error";
        default: return "Unknown";
    }
}

const char* WorldAvatarEntryManager::GetExitResultString(ExitResult result) {
    switch (result) {
        case ExitResult::Success: return "Success";
        case ExitResult::InvalidClient: return "Invalid Client";
        case ExitResult::NotLoggedIn: return "Not Logged In";
        case ExitResult::PartyUpdateFailed: return "Party Update Failed";
        case ExitResult::SystemError: return "System Error";
        default: return "Unknown";
    }
}

/**
 * Exit world function (based on wa_ExitWorld pattern)
 */
bool WorldAvatarEntryManager::ExitWorld(const ClientID* pClientID) {
    if (!ValidateClientExit(pClientID)) {
        LogExitAttempt(pClientID, ExitResult::InvalidClient);
        return false;
    }

    try {
        CPartyPlayer* pPlayer = GetPartyPlayer(pClientID->wIndex);
        if (!pPlayer) {
            LogExitAttempt(pClientID, ExitResult::SystemError);
            return false;
        }

        // Validate client serial matches and player is logged in
        // This would be equivalent to: pLeaver->m_id.dwSerial == v13->dwSerial && pLeaver->m_bLogin
        if (!IsPlayerLoggedIn(pPlayer, nullptr)) {
            LogExitAttempt(pClientID, ExitResult::NotLoggedIn);
            return false;
        }

        // Process party exit if needed
        if (!ProcessPartyExit(pPlayer)) {
            LogExitAttempt(pClientID, ExitResult::PartyUpdateFailed);
            return false;
        }

        LogExitAttempt(pClientID, ExitResult::Success);
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ExitWorld: " << e.what() << std::endl;
        LogExitAttempt(pClientID, ExitResult::SystemError);
        return false;
    }
}

bool WorldAvatarEntryManager::ValidateClientExit(const ClientID* pClientID) {
    return pClientID && pClientID->IsValid() && pClientID->wIndex < MAX_PLAYER_INDEX;
}

bool WorldAvatarEntryManager::ProcessPartyExit(CPartyPlayer* pPlayer) {
    if (!pPlayer) {
        return false;
    }

    // This would handle party management during exit
    // Based on the original wa_ExitWorld implementation
    CPartyPlayer* pNewBoss = nullptr;
    return CPartyPlayer_ExitWorld(pPlayer, &pNewBoss);
}

/**
 * Utility namespace implementation
 */
namespace WorldAvatarUtils {

    bool ValidateAvatarName(const char* name) {
        if (!name || strlen(name) == 0) {
            return false;
        }

        size_t len = strlen(name);
        if (len < 2 || len >= 32) {
            return false;
        }

        // Check for invalid characters (basic validation)
        for (size_t i = 0; i < len; ++i) {
            char c = name[i];
            if (!((c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z') ||
                  (c >= '0' && c <= '9') || c == '_')) {
                return false;
            }
        }

        return !IsReservedName(name);
    }

    bool IsReservedName(const char* name) {
        if (!name) return false;

        // List of reserved names (this would be expanded based on game requirements)
        const char* reservedNames[] = {
            "admin", "gm", "system", "server", "null", "undefined"
        };

        for (const char* reserved : reservedNames) {
            if (_stricmp(name, reserved) == 0) {
                return true;
            }
        }

        return false;
    }

    uint32_t GenerateAvatarSerial() {
        // This would be a proper serial generation system
        // For now, return a timestamp-based value
        return static_cast<uint32_t>(GetTickCount64() & 0xFFFFFFFF);
    }

    std::string FormatAvatarInfo(const WorldAvatarCode* pAvatar) {
        if (!pAvatar) {
            return "Invalid Avatar";
        }

        char buffer[256];
        sprintf_s(buffer, sizeof(buffer),
                 "Avatar[%s] ID:%u:%u Class:%u Level:%u Map:%u Pos:(%.2f,%.2f,%.2f)",
                 pAvatar->m_wszName,
                 pAvatar->m_id.wIndex, pAvatar->m_id.dwSerial,
                 pAvatar->m_byClass, pAvatar->m_byLevel, pAvatar->m_wMapCode,
                 pAvatar->m_fPosition[0], pAvatar->m_fPosition[1], pAvatar->m_fPosition[2]);

        return std::string(buffer);
    }

    bool IsValidPosition(float x, float y, float z) {
        // Basic position validation
        return !std::isnan(x) && !std::isnan(y) && !std::isnan(z) &&
               !std::isinf(x) && !std::isinf(y) && !std::isinf(z) &&
               x >= -100000.0f && x <= 100000.0f &&
               y >= -100000.0f && y <= 100000.0f &&
               z >= -10000.0f && z <= 10000.0f;
    }

    float CalculateDistance(const float pos1[3], const float pos2[3]) {
        if (!pos1 || !pos2) return -1.0f;

        float dx = pos1[0] - pos2[0];
        float dy = pos1[1] - pos2[1];
        float dz = pos1[2] - pos2[2];

        return std::sqrt(dx * dx + dy * dy + dz * dz);
    }

    bool IsInSafeZone(const float position[3], uint16_t mapCode) {
        if (!position || !IsValidPosition(position[0], position[1], position[2])) {
            return false;
        }

        // This would implement actual safe zone checking based on map data
        // For now, return false (no safe zones defined)
        return false;
    }
}

/**
 * Legacy C-style interface functions
 */
extern "C" {
    void wa_EnterWorld(const WorldAvatarCode* pData, uint16_t wZoneIndex) {
        WorldAvatarEntryManager::EnterWorld(pData, wZoneIndex);
    }

    void wa_ExitWorld(const ClientID* pClientID) {
        WorldAvatarEntryManager::ExitWorld(pClientID);
    }
}

/**
 * Placeholder implementations for external CPartyPlayer functions
 * These would be properly implemented based on the actual CPartyPlayer class
 */
namespace {
    bool CPartyPlayer_EnterWorld(CPartyPlayer* pPlayer, const WorldAvatarCode* pData, uint16_t wZoneIndex) {
        // Placeholder implementation
        // This would call the actual CPartyPlayer::EnterWorld method
        std::cout << "[DEBUG] CPartyPlayer::EnterWorld called for zone " << wZoneIndex << std::endl;
        return true;
    }

    bool CPartyPlayer_ExitWorld(CPartyPlayer* pPlayer, CPartyPlayer** ppNewBoss) {
        // Placeholder implementation
        // This would call the actual CPartyPlayer::ExitWorld method
        std::cout << "[DEBUG] CPartyPlayer::ExitWorld called" << std::endl;
        return true;
    }

    bool CPartyPlayer_IsPartyMode(const CPartyPlayer* pPlayer) {
        // Placeholder implementation
        return false;
    }

    CPartyPlayer** CPartyPlayer_GetPtrPartyMember(CPartyPlayer* pPlayer) {
        // Placeholder implementation
        return nullptr;
    }
}
