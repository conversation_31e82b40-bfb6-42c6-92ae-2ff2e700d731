/*
 * Function: ?_Buynode@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@IEAAPEAU_Node@?$_List_nod@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@2@PEAU342@0AEBU?$pair@$$CBHPEAVCAsyncLogInfo@@@2@@Z
 * Address: 0x1403C6500
 */

std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > *__fastcall std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Buynode(std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > *this, std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node *_Next, std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node *_Prev, std::pair<int const ,CAsyncLogInfo *> *_Val)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node *v6; // rdx@4
  std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node *v7; // rdx@4
  std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node *v8; // rdx@4
  __int64 v10; // [sp+0h] [bp-58h]@1
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > *v11; // [sp+20h] [bp-38h]@4
  int v12; // [sp+28h] [bp-30h]@4
  __int64 v13; // [sp+30h] [bp-28h]@4
  std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node **_Ptr; // [sp+38h] [bp-20h]@4
  std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node **v15; // [sp+40h] [bp-18h]@4
  std::pair<int const ,CAsyncLogInfo *> *v16; // [sp+48h] [bp-10h]@4
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > *v17; // [sp+60h] [bp+8h]@1
  std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node *_Vala; // [sp+68h] [bp+10h]@1
  std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node *v19; // [sp+70h] [bp+18h]@1
  std::pair<int const ,CAsyncLogInfo *> *v20; // [sp+78h] [bp+20h]@1

  v20 = _Val;
  v19 = _Prev;
  _Vala = _Next;
  v17 = this;
  v4 = &v10;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v13 = -2i64;
  v11 = (std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > *)std::allocator<std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node>::allocate(&v17->_Alnod, 1ui64);
  v12 = 0;
  _Ptr = std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Nextnode(
           v11,
           v6);
  std::allocator<std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node *>::construct(
    &v17->_Alptr,
    _Ptr,
    &_Vala);
  ++v12;
  v15 = std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Prevnode(
          v11,
          v7);
  std::allocator<std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node *>::construct(
    &v17->_Alptr,
    v15,
    &v19);
  ++v12;
  v16 = std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Myval(
          v11,
          v8);
  std::allocator<std::pair<int const,CAsyncLogInfo *>>::construct(&v17->_Alval, v16, v20);
  return v11;
}
