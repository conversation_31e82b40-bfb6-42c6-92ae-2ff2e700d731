/*
 * Function: ?SendMsg_CurAllUserLogin@CBilling@@IEAAXXZ
 * Address: 0x14028D610
 */

void __fastcall CBilling::SendMsg_CurAllUserLogin(CBilling *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char *v3; // rax@10
  __int64 v4; // [sp+0h] [bp-78h]@1
  __int16 v5; // [sp+20h] [bp-58h]@10
  _SYSTEMTIME *v6; // [sp+28h] [bp-50h]@10
  int v7; // [sp+30h] [bp-48h]@10
  unsigned int j; // [sp+40h] [bp-38h]@5
  CUserDB *v9; // [sp+48h] [bp-30h]@8
  _SYSTEMTIME *v10; // [sp+50h] [bp-28h]@10
  char *v11; // [sp+58h] [bp-20h]@10
  CBillingVtbl *v12; // [sp+60h] [bp-18h]@10
  CBilling *v13; // [sp+80h] [bp+8h]@1

  v13 = this;
  v1 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v13->m_bOper )
  {
    for ( j = 0; j < 0x9E4; ++j )
    {
      v9 = &g_UserDB[j];
      if ( v9->m_bActive )
      {
        if ( !v9->m_byUserDgr )
        {
          v10 = &v9->m_BillingInfo.stEndDate;
          v11 = v9->m_BillingInfo.szCMS;
          v3 = inet_ntoa((struct in_addr)v9->m_dwIP);
          v12 = v13->vfptr;
          v7 = v9->m_BillingInfo.lRemainTime;
          v6 = v10;
          v5 = v9->m_BillingInfo.iType;
          ((void (__fastcall *)(CBilling *, __int64, char *, __int64))v12->SendMsg_Login)(
            v13,
            (__int64)v9->m_szAccountID,
            v3,
            (__int64)v11);
        }
      }
    }
  }
}
