# NexusProtection World Module

This directory contains the refactored world module components from the original decompiled C source code, now compatible with Visual Studio 2022.

## Directory Structure

```
D:\_1.NexusPro\NexusProtection\world\
├── Headers\                           # Header files (.h)
│   └── MonsterEventRespawn.h         # Monster event respawn system
├── Source\                           # Source files (.cpp)
│   └── MonsterEventRespawn.cpp       # Monster event respawn implementation
├── Documents\                        # Documentation
│   └── README.md                     # This file
├── Tests\                           # Unit tests (future)
├── CMakeLists.txt                   # CMake build configuration
└── NexusProtectionWorld.vcxproj     # Visual Studio 2022 project file
```

## Refactoring Status

### ✅ Completed Files

1. **MonsterEventRespawn** (Original: `SetEventRespawnCMonsterEventRespawnQEAA_NXZ_1402A5DE0.c`)
   - **Header**: `Headers\MonsterEventRespawn.h`
   - **Source**: `Source\MonsterEventRespawn.cpp`
   - **Status**: Fully refactored and VS2022 compatible

### 🔄 Pending Files

The following files are queued for refactoring:
- `wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_140046110.c`
- `WorldServiceInformCNetworkEXAEAA_NKPEADZ_1401C0940.c`
- `EnterWorldResultCNetworkEXAEAA_NKPEADZ_1401C06D0.c`
- And many more...

## Key Refactoring Changes

### 1. Modern C++ Standards
- **C++20 compliance** with proper standard library usage
- **RAII principles** for resource management
- **Smart pointers** where appropriate
- **STL containers** instead of raw arrays

### 2. Visual Studio 2022 Compatibility
- **Platform toolset v143**
- **Windows 10 SDK**
- **Unicode character set**
- **Modern compiler flags** (/permissive-, /Zc:__cplusplus)

### 3. Code Structure Improvements
- **Proper header guards** and include organization
- **Namespace usage** for utility functions
- **Const correctness** throughout
- **Error handling** with exceptions and return codes
- **Comprehensive logging** system

### 4. Removed Decompilation Artifacts
- **Assembly-style variable names** (v1, v2, etc.) → meaningful names
- **Stack pointer references** → proper local variables
- **Manual memory initialization loops** → proper constructors
- **Security cookie operations** → modern security practices
- **Calling conventions** (__fastcall) → standard C++

## Building the Project

### Option 1: Visual Studio 2022 (Recommended)
1. Open `NexusProtectionWorld.vcxproj` in Visual Studio 2022
2. Select your target platform (x86/x64) and configuration (Debug/Release)
3. Build → Build Solution (Ctrl+Shift+B)

### Option 2: CMake
```bash
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release
```

### Option 3: Command Line (MSBuild)
```cmd
msbuild NexusProtectionWorld.vcxproj /p:Configuration=Release /p:Platform=x64
```

## Project Configuration

### Compiler Settings
- **Language Standard**: C++20 (ISO/IEC 14882:2020)
- **Runtime Library**: Multi-threaded (/MT for Release, /MTd for Debug)
- **Warning Level**: Level 3 (/W3)
- **Conformance Mode**: Yes (/permissive-)

### Preprocessor Definitions
- `WIN32_LEAN_AND_MEAN` - Exclude rarely-used Windows headers
- `NOMINMAX` - Prevent Windows.h from defining min/max macros
- `_CRT_SECURE_NO_WARNINGS` - Disable CRT security warnings
- `NEXUS_PROTECTION_WORLD_EXPORTS` - Module-specific exports

### Dependencies
- **Windows SDK 10.0** or later
- **kernel32.lib** - Core Windows API
- **user32.lib** - User interface functions
- **advapi32.lib** - Advanced Windows API

## Usage Example

```cpp
#include "MonsterEventRespawn.h"

int main() {
    // Create monster event respawn system
    auto eventSystem = std::make_unique<CMonsterEventRespawn>();
    
    // Load event configurations from .ini files
    if (!eventSystem->SetEventRespawn()) {
        std::cerr << "Failed to load event respawn configurations" << std::endl;
        return 1;
    }
    
    // Start a specific event
    char errorBuffer[256];
    if (!eventSystem->StartRespawnEvent("BossEvent01", errorBuffer)) {
        std::cerr << "Failed to start event: " << errorBuffer << std::endl;
        return 1;
    }
    
    std::cout << "Event system initialized successfully!" << std::endl;
    std::cout << "Loaded " << eventSystem->GetLoadedEventCount() << " events" << std::endl;
    
    return 0;
}
```

## Configuration Files

The system expects `.ini` configuration files in the `EventRespawn` directory:

```
EventRespawn/
├── BossEvent01.ini
├── SpecialEvent.ini
└── ...
```

### Example Configuration File Format

```ini
[MONSTER]
set=2
code0=BOSS_DRAGON
num0=1
code1=MINION_ORC
num1=5

[POSITION]
map=DUNGEON_01
x=1000
y=2000
z=100

[TERM]
hour=1
min=30
sec=0

[OPTION]
kill after stop=1
exp penalty=0
exp reward=1
item loot=1

[REWARD ITEM]
item code 1=RARE_SWORD
monster 1=BOSS_DRAGON
item % 1=10
```

## Next Steps

1. **Verify compilation** of the current refactored file
2. **Test functionality** with sample configuration files
3. **Proceed to next file** in the refactoring queue
4. **Integrate with other modules** as they are completed

## Notes

- All original functionality has been preserved while modernizing the code
- Error handling has been significantly improved
- The code is now maintainable and extensible
- Performance should be equivalent or better than the original
- Memory safety has been enhanced through modern C++ practices

## File Naming Convention

- **Original**: `decompiled source code\world\filename.c`
- **Header**: `D:\_1.NexusPro\NexusProtection\world\Headers\filename.h`
- **Source**: `D:\_1.NexusPro\NexusProtection\world\Source\filename.cpp`

This structure maintains clear separation of concerns and follows modern C++ project organization standards.
