# CMakeLists.txt for NexusProtection World Module
# Compatible with Visual Studio 2022

cmake_minimum_required(VERSION 3.20)

# Project configuration
project(NexusProtectionWorld 
    VERSION 1.0.0
    DESCRIPTION "NexusProtection World Module - Refactored for VS2022"
    LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Platform-specific settings for Windows/Visual Studio
if(WIN32)
    set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
    add_definitions(-DWIN32_LEAN_AND_MEAN)
    add_definitions(-DNOMINMAX)
    add_definitions(-D_CRT_SECURE_NO_WARNINGS)
endif()

# Compiler-specific settings for MSVC
if(MSVC)
    # Use static runtime library
    set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>")
    
    # Warning level and other flags
    add_compile_options(/W3)
    add_compile_options(/permissive-)
    add_compile_options(/Zc:__cplusplus)
    
    # Debug configuration
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /Zi /RTC1")
    
    # Release configuration
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /DNDEBUG")
endif()

# Source files
set(WORLD_SOURCES
    Source/MonsterEventRespawn.cpp
    # Add other .cpp files here as they are refactored
)

# Header files
set(WORLD_HEADERS
    Headers/MonsterEventRespawn.h
    # Add other .h files here as they are refactored
)

# Create static library
add_library(NexusProtectionWorld STATIC
    ${WORLD_SOURCES}
    ${WORLD_HEADERS}
)

# Include directories
target_include_directories(NexusProtectionWorld
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/Headers
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/Source
        ${CMAKE_CURRENT_SOURCE_DIR}/../common/Headers
        ${CMAKE_CURRENT_SOURCE_DIR}/../database/Headers
        ${CMAKE_CURRENT_SOURCE_DIR}/../system/Headers
)

# Link libraries
if(WIN32)
    target_link_libraries(NexusProtectionWorld
        PRIVATE
            kernel32
            user32
            advapi32
    )
endif()

# Preprocessor definitions
target_compile_definitions(NexusProtectionWorld
    PRIVATE
        $<$<CONFIG:Debug>:_DEBUG>
        $<$<CONFIG:Release>:NDEBUG>
        NEXUS_PROTECTION_WORLD_EXPORTS
)

# Set target properties
set_target_properties(NexusProtectionWorld PROPERTIES
    OUTPUT_NAME "NexusProtectionWorld"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

# Install rules
install(TARGETS NexusProtectionWorld
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${WORLD_HEADERS}
    DESTINATION include/world
)

# Optional: Create a test executable
if(BUILD_TESTING)
    add_executable(WorldModuleTest
        Tests/test_monster_event_respawn.cpp
    )
    
    target_link_libraries(WorldModuleTest
        PRIVATE
            NexusProtectionWorld
    )
    
    target_include_directories(WorldModuleTest
        PRIVATE
            ${CMAKE_CURRENT_SOURCE_DIR}/Headers
    )
    
    # Add test
    enable_testing()
    add_test(NAME WorldModuleTest COMMAND WorldModuleTest)
endif()

# Documentation
if(BUILD_DOCUMENTATION)
    find_package(Doxygen)
    if(DOXYGEN_FOUND)
        set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/Documents/Doxyfile.in)
        set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
        
        configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
        
        add_custom_target(WorldModuleDocs ALL
            COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation with Doxygen"
            VERBATIM
        )
    endif()
endif()

# Custom target for organizing files in IDE
source_group("Header Files" FILES ${WORLD_HEADERS})
source_group("Source Files" FILES ${WORLD_SOURCES})

# Print configuration summary
message(STATUS "NexusProtection World Module Configuration:")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Platform: ${CMAKE_SYSTEM_NAME}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID}")
if(MSVC)
    message(STATUS "  MSVC Version: ${MSVC_VERSION}")
    message(STATUS "  Platform Toolset: v143")
endif()
