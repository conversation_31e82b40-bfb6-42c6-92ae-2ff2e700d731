/*
 * Function: ?Validate@?$DL_PrivateKeyImpl@VDL_GroupParameters_DSA@CryptoPP@@@CryptoPP@@UEBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x140568460
 */

char __fastcall CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_DSA>::Validate(__int64 a1, __int64 a2, unsigned int a3)
{
  __int64 v3; // rax@1
  char v4; // ST30_1@1
  __int64 v5; // rax@1
  CryptoPP::Integer *v6; // rax@1
  CryptoPP::Integer *v7; // rax@1
  CryptoPP::Integer *a; // [sp+20h] [bp-A8h]@1
  CryptoPP::Integer *b; // [sp+28h] [bp-A0h]@1
  char v11; // [sp+30h] [bp-98h]@6
  CryptoPP::Integer v12; // [sp+40h] [bp-88h]@8
  int v13; // [sp+68h] [bp-60h]@1
  __int64 v14; // [sp+70h] [bp-58h]@1
  int (__fastcall **v15)(_QWORD); // [sp+78h] [bp-50h]@1
  int (__fastcall **v16)(_QWORD); // [sp+80h] [bp-48h]@1
  __int64 v17; // [sp+88h] [bp-40h]@1
  __int64 v18; // [sp+90h] [bp-38h]@1
  int v19; // [sp+98h] [bp-30h]@4
  CryptoPP::Integer *v20; // [sp+A0h] [bp-28h]@8
  CryptoPP::Integer *v21; // [sp+A8h] [bp-20h]@8
  CryptoPP::Integer *v22; // [sp+B0h] [bp-18h]@8
  int v23; // [sp+B8h] [bp-10h]@9
  __int64 v24; // [sp+D0h] [bp+8h]@1
  __int64 v25; // [sp+D8h] [bp+10h]@1
  unsigned int v26; // [sp+E0h] [bp+18h]@1

  v26 = a3;
  v25 = a2;
  v24 = a1;
  v14 = -2i64;
  v13 = 0;
  v15 = *(int (__fastcall ***)(_QWORD))(a1 - 392);
  LODWORD(v3) = (*v15)(a1 - 392);
  v4 = (*(int (__fastcall **)(signed __int64, __int64, _QWORD))(*(_QWORD *)(v3
                                                                          + *(_DWORD *)(*(_QWORD *)(v3 + 8) + 4i64)
                                                                          + 8)
                                                              + 24i64))(
         v3 + *(_DWORD *)(*(_QWORD *)(v3 + 8) + 4i64) + 8,
         v25,
         v26);
  v16 = *(int (__fastcall ***)(_QWORD))(v24 - 392);
  LODWORD(v5) = (*v16)(v24 - 392);
  v17 = v5;
  LODWORD(v6) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v5 + 64i64))(v5);
  b = v6;
  v18 = *(_QWORD *)(v24 - 392);
  LODWORD(v7) = (*(int (__fastcall **)(signed __int64))(v18 + 16))(v24 - 392);
  a = v7;
  v19 = v4 && CryptoPP::Integer::IsPositive(v7) && CryptoPP::operator<(a, b);
  v11 = v19;
  if ( v26 >= 1 )
  {
    v23 = (_BYTE)v19
       && (v20 = (CryptoPP::Integer *)CryptoPP::Integer::One(),
           v21 = CryptoPP::Integer::Gcd(&v12, a, b),
           v22 = v21,
           v13 |= 1u,
           CryptoPP::operator==(v21, v20));
    v11 = v23;
    if ( v13 & 1 )
    {
      v13 &= 0xFFFFFFFE;
      CryptoPP::Integer::~Integer(&v12);
    }
  }
  return v11;
}
