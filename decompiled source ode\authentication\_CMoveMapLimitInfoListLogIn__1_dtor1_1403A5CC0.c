/*
 * Function: _CMoveMapLimitInfoList::LogIn_::_1_::dtor$1
 * Address: 0x1403A5CC0
 */

void __fastcall CMoveMapLimitInfoList::LogIn_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>((std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)(a2 + 56));
}
