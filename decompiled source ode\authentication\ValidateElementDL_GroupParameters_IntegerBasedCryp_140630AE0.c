/*
 * Function: ?ValidateElement@DL_GroupParameters_IntegerBased@CryptoPP@@UEBA_NIAEBVInteger@2@PEBV?$DL_FixedBasePrecomputation@VInteger@CryptoPP@@@2@@Z
 * Address: 0x140630AE0
 */

char __fastcall CryptoPP::DL_GroupParameters_IntegerBased::ValidateElement(__int64 a1, unsigned int a2, CryptoPP::Integer *a3, __int64 *a4)
{
  CryptoPP::Integer *v4; // rax@1
  __int64 v5; // rax@1
  __int64 v6; // rax@12
  __int64 v7; // rax@12
  CryptoPP::Integer *v8; // rax@12
  const struct CryptoPP::Integer *v9; // r8@20
  __int64 v10; // rax@37
  struct CryptoPP::Integer *v11; // rax@37
  struct CryptoPP::Integer *v12; // rax@38
  const struct CryptoPP::Integer *v13; // r8@48
  CryptoPP::Integer *b; // [sp+20h] [bp-258h]@1
  __int64 v16; // [sp+28h] [bp-250h]@1
  char v17; // [sp+30h] [bp-248h]@9
  CryptoPP::Integer v18; // [sp+38h] [bp-240h]@39
  CryptoPP::Integer v19; // [sp+60h] [bp-218h]@12
  CryptoPP::Integer v20; // [sp+88h] [bp-1F0h]@20
  CryptoPP::Integer result; // [sp+B0h] [bp-1C8h]@20
  CryptoPP::Integer v22; // [sp+D8h] [bp-1A0h]@20
  struct CryptoPP::Integer *v23; // [sp+100h] [bp-178h]@39
  CryptoPP::Integer v24; // [sp+108h] [bp-170h]@37
  CryptoPP::Integer v25; // [sp+130h] [bp-148h]@38
  int v26; // [sp+158h] [bp-120h]@1
  __int64 v27; // [sp+160h] [bp-118h]@1
  __int64 v28; // [sp+168h] [bp-110h]@1
  __int64 v29; // [sp+170h] [bp-108h]@1
  __int64 v30; // [sp+178h] [bp-100h]@1
  bool v31; // [sp+180h] [bp-F8h]@2
  __int64 v32; // [sp+188h] [bp-F0h]@6
  int v33; // [sp+190h] [bp-E8h]@7
  __int64 v34; // [sp+198h] [bp-E0h]@12
  __int64 v35; // [sp+1A0h] [bp-D8h]@12
  CryptoPP::Integer *v36; // [sp+1A8h] [bp-D0h]@12
  CryptoPP::Integer *v37; // [sp+1B0h] [bp-C8h]@12
  int v38; // [sp+1B8h] [bp-C0h]@13
  __int64 v39; // [sp+1C0h] [bp-B8h]@18
  CryptoPP::Integer *v40; // [sp+1C8h] [bp-B0h]@20
  CryptoPP::Integer *v41; // [sp+1D0h] [bp-A8h]@20
  CryptoPP::Integer *v42; // [sp+1D8h] [bp-A0h]@20
  CryptoPP *v43; // [sp+1E0h] [bp-98h]@20
  int v44; // [sp+1E8h] [bp-90h]@21
  __int64 v45; // [sp+1F0h] [bp-88h]@29
  __int64 v46; // [sp+1F8h] [bp-80h]@31
  int v47; // [sp+200h] [bp-78h]@32
  __int64 v48; // [sp+208h] [bp-70h]@37
  __int64 v49; // [sp+210h] [bp-68h]@37
  struct CryptoPP::Integer *v50; // [sp+218h] [bp-60h]@37
  struct CryptoPP::Integer *v51; // [sp+220h] [bp-58h]@37
  struct CryptoPP::Integer *v52; // [sp+228h] [bp-50h]@37
  __int64 v53; // [sp+230h] [bp-48h]@38
  struct CryptoPP::Integer *v54; // [sp+238h] [bp-40h]@38
  struct CryptoPP::Integer *v55; // [sp+240h] [bp-38h]@38
  __int64 v56; // [sp+248h] [bp-30h]@44
  int v57; // [sp+250h] [bp-28h]@45
  __int64 v58; // [sp+258h] [bp-20h]@48
  int v59; // [sp+260h] [bp-18h]@51
  __int64 v60; // [sp+280h] [bp+8h]@1
  unsigned int v61; // [sp+288h] [bp+10h]@1
  CryptoPP::Integer *a; // [sp+290h] [bp+18h]@1
  __int64 *v63; // [sp+298h] [bp+20h]@1

  v63 = a4;
  a = a3;
  v61 = a2;
  v60 = a1;
  v27 = -2i64;
  v26 = 0;
  v28 = *(_QWORD *)(a1 - 8);
  LODWORD(v4) = (*(int (__fastcall **)(signed __int64))(v28 + 32))(a1 - 8);
  b = v4;
  v29 = *(_QWORD *)v60;
  LODWORD(v5) = (*(int (__fastcall **)(__int64))(v29 + 64))(v60);
  v16 = v5;
  v30 = *(_QWORD *)(v60 - 8);
  if ( (*(int (__fastcall **)(signed __int64))(v30 + 48))(v60 - 8) == 1 )
    v31 = CryptoPP::Integer::IsPositive(a);
  else
    v31 = CryptoPP::Integer::NotNegative(a);
  v33 = v31
     && CryptoPP::operator<(a, b)
     && (v32 = *(_QWORD *)v60,
         !(unsigned __int8)(*(int (__fastcall **)(__int64, CryptoPP::Integer *))(v32 + 152))(v60, a));
  v17 = v33;
  if ( v61 >= 1 && v63 )
  {
    v38 = (_BYTE)v33
       && (LODWORD(v6) = CryptoPP::Integer::One(),
           v34 = v6,
           LODWORD(v7) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v60 + 40i64))(v60),
           v35 = *v63,
           LODWORD(v8) = (*(int (__fastcall **)(__int64 *, CryptoPP::Integer *, __int64, __int64))(v35 + 48))(
                           v63,
                           &v19,
                           v7,
                           v34),
           v36 = v8,
           v37 = v8,
           v26 |= 1u,
           CryptoPP::operator==(v8, a));
    v17 = v38;
    if ( v26 & 1 )
    {
      v26 &= 0xFFFFFFFE;
      CryptoPP::Integer::~Integer(&v19);
    }
  }
  if ( v61 >= 2 )
  {
    v39 = *(_QWORD *)(v60 - 8);
    if ( (*(int (__fastcall **)(signed __int64))(v39 + 48))(v60 - 8) == 2 )
    {
      v44 = v17
         && (CryptoPP::Integer::Integer(&v20, 4),
             v26 |= 2u,
             v40 = CryptoPP::operator*(&result, a, a),
             v41 = v40,
             v26 |= 4u,
             v42 = CryptoPP::operator-(&v22, v40, &v20),
             v43 = (CryptoPP *)v42,
             v26 |= 8u,
             (unsigned int)CryptoPP::Jacobi((CryptoPP *)v42, b, v9) == -1);
      v17 = v44;
      if ( v26 & 8 )
      {
        v26 &= 0xFFFFFFF7;
        CryptoPP::Integer::~Integer(&v22);
      }
      if ( v26 & 4 )
      {
        v26 &= 0xFFFFFFFB;
        CryptoPP::Integer::~Integer(&result);
      }
      if ( v26 & 2 )
      {
        v26 &= 0xFFFFFFFD;
        CryptoPP::Integer::~Integer(&v20);
      }
    }
    v45 = *(_QWORD *)(v60 - 8);
    v47 = (*(int (__fastcall **)(signed __int64))(v45 + 48))(v60 - 8) == 2 && v61 >= 3
       || (v46 = *(_QWORD *)v60, !(unsigned __int8)(*(int (__fastcall **)(__int64))(v46 + 144))(v60));
    if ( (_BYTE)v47 && v17 )
    {
      if ( v63 )
      {
        v48 = *(_QWORD *)v60;
        LODWORD(v10) = (*(int (__fastcall **)(__int64))(v48 + 40))(v60);
        v49 = *v63;
        LODWORD(v11) = (*(int (__fastcall **)(__int64 *, CryptoPP::Integer *, __int64, __int64))(v49 + 48))(
                         v63,
                         &v24,
                         v10,
                         v16);
        v50 = v11;
        v51 = v11;
        v26 |= 0x10u;
        v52 = v11;
      }
      else
      {
        v53 = *(_QWORD *)v60;
        LODWORD(v12) = (*(int (__fastcall **)(__int64, CryptoPP::Integer *, CryptoPP::Integer *, __int64))(v53 + 32))(
                         v60,
                         &v25,
                         a,
                         v16);
        v54 = v12;
        v55 = v12;
        v26 |= 0x20u;
        v52 = v12;
      }
      v23 = v52;
      CryptoPP::Integer::Integer(&v18, v52);
      if ( v26 & 0x20 )
      {
        v26 &= 0xFFFFFFDF;
        CryptoPP::Integer::~Integer(&v25);
      }
      if ( v26 & 0x10 )
      {
        v26 &= 0xFFFFFFEF;
        CryptoPP::Integer::~Integer(&v24);
      }
      v57 = v17
         && (v56 = *(_QWORD *)v60,
             (unsigned __int8)(*(int (__fastcall **)(__int64, CryptoPP::Integer *))(v56 + 152))(v60, &v18));
      v17 = v57;
      CryptoPP::Integer::~Integer(&v18);
    }
    else
    {
      v58 = *(_QWORD *)(v60 - 8);
      if ( (*(int (__fastcall **)(signed __int64))(v58 + 48))(v60 - 8) == 1 )
      {
        v59 = v17 && (unsigned int)CryptoPP::Jacobi((CryptoPP *)a, b, v13) == 1;
        v17 = v59;
      }
    }
  }
  return v17;
}
