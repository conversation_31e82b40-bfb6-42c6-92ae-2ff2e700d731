/*
 * Function: j_?size@?$_Hash@V?$_Hmap_traits@HPEAVCAsyncLogInfo@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@$0A@@stdext@@@stdext@@QEBA_KXZ
 * Address: 0x14000896D
 */

unsigned __int64 __fastcall stdext::_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>,0>>::size(stdext::_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CAsyncLogInfo *> >,0> > *this)
{
  return stdext::_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>,0>>::size(this);
}
