/*
 * Function: ?OnLoopSession@CHackShieldExSystem@@UEAAXH@Z
 * Address: 0x1404171A0
 */

void __fastcall CHackShieldExSystem::OnLoopSession(CHackShieldExSystem *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  BASE_HACKSHEILD_PARAM *v5; // [sp+20h] [bp-18h]@4
  CUserDB *v6; // [sp+28h] [bp-10h]@6
  CHackShieldExSystem *v7; // [sp+40h] [bp+8h]@1
  int na; // [sp+48h] [bp+10h]@1

  na = n;
  v7 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = CHackShieldExSystem::GetParam(v7, n);
  if ( v5 && (unsigned __int8)((int (__fastcall *)(BASE_HACKSHEILD_PARAM *))v5->vfptr->IsLogPass)(v5) )
  {
    v6 = &g_UserDB[na];
    if ( v6->m_bActive )
      ((void (__fastcall *)(BASE_HACKSHEILD_PARAM *))v5->vfptr->OnLoop)(v5);
  }
}
