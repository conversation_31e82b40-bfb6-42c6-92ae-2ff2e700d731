/*
 * WorldAvatarEntry.h - World Avatar Entry System
 * Refactored for Visual Studio 2022 compatibility
 * Original: wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_140046110.c
 */

#pragma once

#include <windows.h>
#include <cstdint>
#include <string>
#include <memory>

// Forward declarations
class CPartyPlayer;
class CPlayer;

// Avatar identification structure
struct AvatarID {
    uint16_t wIndex;        // Avatar index
    uint32_t dwSerial;      // Avatar serial number
    
    AvatarID() : wIndex(0), dwSerial(0) {}
    AvatarID(uint16_t index, uint32_t serial) : wIndex(index), dwSerial(serial) {}
    
    bool IsValid() const { return wIndex != 0 && dwSerial != 0; }
    void Reset() { wIndex = 0; dwSerial = 0; }
};

// World Avatar Code structure (modernized from _WA_AVATOR_CODE)
struct WorldAvatarCode {
    AvatarID m_id;                      // Avatar identification
    char m_wszName[32];                 // Avatar name (wide string in original)
    uint8_t m_byClass;                  // Character class
    uint8_t m_byLevel;                  // Character level
    uint16_t m_wMapCode;                // Current map code
    float m_fPosition[3];               // X, Y, Z position
    uint32_t m_dwGuildSerial;           // Guild serial number
    uint8_t m_byPvpMode;                // PvP mode flag
    uint8_t m_byPartyMode;              // Party mode flag
    uint8_t m_byReserved[2];            // Padding for alignment
    
    // Constructor
    WorldAvatarCode();
    
    // Utility methods
    bool IsValidAvatar() const;
    void Reset();
    void SetPosition(float x, float y, float z);
    void SetName(const char* name);
    
    // Validation methods
    bool IsValidClass() const;
    bool IsValidLevel() const;
    bool IsValidMap() const;
};

// Client ID structure (modernized from _CLID)
struct ClientID {
    uint16_t wIndex;        // Client index
    uint32_t dwSerial;      // Client serial number
    
    ClientID() : wIndex(0), dwSerial(0) {}
    ClientID(uint16_t index, uint32_t serial) : wIndex(index), dwSerial(serial) {}
    
    bool IsValid() const { return wIndex != 0 && dwSerial != 0; }
    void Reset() { wIndex = 0; dwSerial = 0; }
    bool operator==(const ClientID& other) const {
        return wIndex == other.wIndex && dwSerial == other.dwSerial;
    }
};

/**
 * World Avatar Entry Manager
 * Handles avatar entry and exit from the world system
 */
class WorldAvatarEntryManager {
public:
    // Constructor/Destructor
    WorldAvatarEntryManager();
    virtual ~WorldAvatarEntryManager();

    // Main entry/exit functions
    static bool EnterWorld(const WorldAvatarCode* pAvatarData, uint16_t wZoneIndex);
    static bool ExitWorld(const ClientID* pClientID);

    // Validation functions
    static bool ValidateAvatarEntry(const WorldAvatarCode* pAvatarData, uint16_t wZoneIndex);
    static bool ValidateClientExit(const ClientID* pClientID);

    // Party management integration
    static CPartyPlayer* GetPartyPlayer(uint16_t wIndex);
    static bool IsPlayerLoggedIn(const CPartyPlayer* pPlayer, const WorldAvatarCode* pAvatarData);

    // Zone management
    static bool IsValidZoneIndex(uint16_t wZoneIndex);
    static bool CanEnterZone(const WorldAvatarCode* pAvatarData, uint16_t wZoneIndex);

    // Error handling
    enum class EntryResult {
        Success = 0,
        InvalidAvatar,
        InvalidZone,
        AlreadyLoggedIn,
        PartyConflict,
        SystemError
    };

    enum class ExitResult {
        Success = 0,
        InvalidClient,
        NotLoggedIn,
        PartyUpdateFailed,
        SystemError
    };

    static const char* GetEntryResultString(EntryResult result);
    static const char* GetExitResultString(ExitResult result);

private:
    // Constants
    static constexpr uint16_t MAX_ZONE_INDEX = 1000;
    static constexpr uint16_t MAX_PLAYER_INDEX = 2532;
    static constexpr size_t PARTY_PLAYER_SIZE = 128;

    // Helper methods
    static bool InitializeAvatarEntry(CPartyPlayer* pPlayer, const WorldAvatarCode* pAvatarData, uint16_t wZoneIndex);
    static bool ProcessPartyExit(CPartyPlayer* pPlayer);
    static void LogEntryAttempt(const WorldAvatarCode* pAvatarData, uint16_t wZoneIndex, EntryResult result);
    static void LogExitAttempt(const ClientID* pClientID, ExitResult result);

    // Disable copy constructor and assignment operator
    WorldAvatarEntryManager(const WorldAvatarEntryManager&) = delete;
    WorldAvatarEntryManager& operator=(const WorldAvatarEntryManager&) = delete;
};

// Global functions (C-style interface for compatibility)
extern "C" {
    /**
     * Legacy C-style entry point for world avatar entry
     * @param pData - Avatar data structure
     * @param wZoneIndex - Target zone index
     */
    void wa_EnterWorld(const WorldAvatarCode* pData, uint16_t wZoneIndex);

    /**
     * Legacy C-style entry point for world avatar exit
     * @param pClientID - Client identification
     */
    void wa_ExitWorld(const ClientID* pClientID);
}

// Utility functions
namespace WorldAvatarUtils {
    bool ValidateAvatarName(const char* name);
    bool IsReservedName(const char* name);
    uint32_t GenerateAvatarSerial();
    std::string FormatAvatarInfo(const WorldAvatarCode* pAvatar);
    
    // Position utilities
    bool IsValidPosition(float x, float y, float z);
    float CalculateDistance(const float pos1[3], const float pos2[3]);
    bool IsInSafeZone(const float position[3], uint16_t mapCode);
}

// External references (these would be properly defined in the actual implementation)
extern CPartyPlayer g_PartyPlayer[];  // Global party player array
