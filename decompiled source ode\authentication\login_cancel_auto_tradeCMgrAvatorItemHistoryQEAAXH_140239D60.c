/*
 * Function: ?login_cancel_auto_trade@CMgrAvatorItemHistory@@QEAAXHKPEAU_db_con@_STORAGE_LIST@@_JPEAD@Z
 * Address: 0x140239D60
 */

void __fastcall CMgrAvatorItemHistory::login_cancel_auto_trade(CMgrAvatorItemHistory *this, int n, unsigned int dwRegistSerial, _STORAGE_LIST::_db_con *pRegItem, __int64 tResultTime, char *pszFileName)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char *v8; // rax@5
  char *v9; // rax@6
  int v10; // edx@6
  int v11; // er8@6
  __int64 v12; // [sp+0h] [bp-C8h]@1
  unsigned int v13; // [sp+20h] [bp-A8h]@5
  char *v14; // [sp+28h] [bp-A0h]@5
  unsigned __int64 v15; // [sp+30h] [bp-98h]@5
  char *v16; // [sp+38h] [bp-90h]@5
  unsigned __int64 v17; // [sp+40h] [bp-88h]@5
  char *v18; // [sp+48h] [bp-80h]@5
  char *v19; // [sp+50h] [bp-78h]@5
  unsigned __int64 v20; // [sp+58h] [bp-70h]@6
  char *v21; // [sp+60h] [bp-68h]@6
  unsigned __int64 v22; // [sp+68h] [bp-60h]@6
  char *v23; // [sp+70h] [bp-58h]@6
  char *v24; // [sp+78h] [bp-50h]@6
  _base_fld *v25; // [sp+80h] [bp-48h]@4
  tm *v26; // [sp+88h] [bp-40h]@4
  char *v27; // [sp+90h] [bp-38h]@5
  char *v28; // [sp+98h] [bp-30h]@5
  int nTableCode; // [sp+A0h] [bp-28h]@5
  char *v30; // [sp+A8h] [bp-20h]@6
  char *v31; // [sp+B0h] [bp-18h]@6
  int v32; // [sp+B8h] [bp-10h]@6
  CMgrAvatorItemHistory *v33; // [sp+D0h] [bp+8h]@1
  unsigned int v34; // [sp+E0h] [bp+18h]@1
  _STORAGE_LIST::_db_con *v35; // [sp+E8h] [bp+20h]@1

  v35 = pRegItem;
  v34 = dwRegistSerial;
  v33 = this;
  v6 = &v12;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v25 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pRegItem->m_byTableCode, pRegItem->m_wItemIndex);
  sBuf[0] = 0;
  v26 = localtime_5(&tResultTime);
  if ( v26 )
  {
    v30 = v33->m_szCurTime;
    v31 = v33->m_szCurDate;
    v32 = v35->m_byTableCode;
    v9 = DisplayItemUpgInfo(v32, v35->m_dwLv);
    v10 = v26->tm_mon + 1;
    v11 = v26->tm_year;
    v24 = v30;
    v23 = v31;
    v22 = v35->m_lnUID;
    v21 = v9;
    v20 = v35->m_dwDur;
    v19 = v25->m_strCode;
    LODWORD(v18) = v34;
    LODWORD(v17) = v26->tm_sec;
    LODWORD(v16) = v26->tm_min;
    LODWORD(v15) = v26->tm_hour;
    LODWORD(v14) = v26->tm_mday;
    v13 = v10;
    sprintf_s(
      sBuf,
      0x2800ui64,
      "TIMEOUT_AUTO_TRADE: login canceldate(%04d-%02d-%02d %02d:%02d:%02d) reg(%u) %s_%u_@%s[%I64u] [%s %s]\r\n",
      (unsigned int)(v11 + 1900));
  }
  else
  {
    v27 = v33->m_szCurTime;
    v28 = v33->m_szCurDate;
    nTableCode = v35->m_byTableCode;
    v8 = DisplayItemUpgInfo(nTableCode, v35->m_dwLv);
    v19 = v27;
    v18 = v28;
    v17 = v35->m_lnUID;
    v16 = v8;
    v15 = v35->m_dwDur;
    v14 = v25->m_strCode;
    v13 = v34;
    sprintf_s(
      sBuf,
      0x2800ui64,
      "TIMEOUT_AUTO_TRADE: login canceldate(invalid(%u)) reg(%u) %s_%u_@%s[%I64u] [%s %s]\r\n",
      tResultTime);
  }
  strcat_s(sData, 0x4E20ui64, sBuf);
}
