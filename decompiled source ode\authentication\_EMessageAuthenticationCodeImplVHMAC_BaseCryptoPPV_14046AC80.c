/*
 * Function: ??_E?$MessageAuthenticationCodeImpl@VHMAC_Base@CryptoPP@@V?$HMAC@VSHA1@CryptoPP@@@2@@CryptoPP@@W7EAAPEAXI@Z
 * Address: 0x14046AC80
 */

void *__fastcall CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>::`vector deleting destructor'(__int64 a1, unsigned int a2)
{
  return CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>::`vector deleting destructor'(
           (CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1> > *)(a1 - 8),
           a2);
}
